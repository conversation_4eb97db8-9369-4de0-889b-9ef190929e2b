'use client';

import { Edit, Trash2, Eye,AppWindow } from 'lucide-react';
import { DoctorDetailResponse } from '@/types/doctor';


interface DoctorTableProps {
    doctors: DoctorDetailResponse[];
    onView: (doctor: DoctorDetailResponse) => void;
    onEdit: (doctor: DoctorDetailResponse) => void;
    onDelete: (id: number) => void;
    onSchedule: (doctor: DoctorDetailResponse) => void;
}

export const DoctorTable = ({ doctors, onView, onEdit, onDelete,onSchedule }: DoctorTableProps) => {
    const getStatusColor = (isAvailable: boolean) => {
        return isAvailable ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
    };

    const getStatusText = (isAvailable: boolean) => {
        return isAvailable ? 'Có sẵn' : 'Nghỉ';
    };

    return (
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Bác sĩ
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Khoa
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Email
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Trạng thái
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Thao tác
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {doctors.map((doctor) => (
                            <tr key={doctor.doctorId} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center">
                                        <div className="h-10 w-10 flex-shrink-0">
                                            <img
                                                className="h-10 w-10 rounded-full object-cover"
                                                src={doctor.userAvatar || '/default-avatar.png'}
                                                alt={doctor.fullName}
                                            />
                                        </div>
                                        <div className="ml-4">
                                            <div className="text-sm font-medium text-gray-900">{doctor.fullName}</div>
                                            <div className="text-sm text-gray-500">ID: {doctor.doctorId}</div>
                                        </div>
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">{doctor.specialty.specialtyName || 'Không rõ'}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">{doctor.email}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(doctor.isAvailable)}`}>
                                        {getStatusText(doctor.isAvailable)}
                                    </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div className="flex space-x-2">
                                        <button
                                            onClick={() => onView(doctor)}
                                            className="text-blue-600 hover:text-blue-900"
                                        >
                                            <Eye className="w-4 h-4" />
                                        </button>
                                        <button
                                            onClick={() => onEdit(doctor)}
                                            className="text-green-600 hover:text-green-900"
                                        >
                                            <Edit className="w-4 h-4" />
                                        </button>
                                        <button
                                            onClick={() => onDelete(doctor.doctorId)}
                                            className="text-red-600 hover:text-red-900"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                        </button>
                                        <button
                                            onClick={() => onSchedule(doctor)}
                                            className="text-green-600 hover:text-green-900"
                                        >
                                            <AppWindow className="w-4 h-4" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};
