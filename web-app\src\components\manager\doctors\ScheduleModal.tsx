'use client';

import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { DoctorDetailResponse } from '@/types/doctor';
import { getDoctorWorkingSlots } from '@/services/doctorService';


interface WorkingTimeSlotResponse {
  slotId: number;
  slotTime: string;
  slotTimeFormatted: string;
  isAvailable: boolean;
}

interface ScheduleModalProps {
  isOpen: boolean;
  doctor: DoctorDetailResponse | null;
  onClose: () => void;
  // onSubmit: (data: WorkScheduleDto) => void;
}

export const ScheduleModal = ({ isOpen, doctor, onClose }: ScheduleModalProps) => {
  const [workDate, setWorkDate] = useState('');
  const [timeSlots, setTimeSlots] = useState<string[]>(['']);
  const [availableSlots, setAvailableSlots] = useState<WorkingTimeSlotResponse[]>([]);

 useEffect(() => {
  const fetchSlots = async () => {
    if (!doctor || !workDate) return;
    try {
      const res = await getDoctorWorkingSlots(doctor.doctorId, workDate);
      const dayData = res.result.availableDays.find(
        (day: any) => day.date === workDate
      );
      const slots = (dayData?.availableSlots || [])
        .filter((s: WorkingTimeSlotResponse) => s.isAvailable)
        .map((s: WorkingTimeSlotResponse) => s.slotTime);
      setAvailableSlots(dayData?.availableSlots || []);
      setTimeSlots(slots); // ✅ Gán trực tiếp vào timeSlots luôn
    } catch (err) {
      toast.error('Không thể tải khung giờ làm việc');
    }
  };
  fetchSlots();
}, [doctor, workDate]);


 

  const handleSlotTimeChange = (index: number, newTime: string) => {
  const updated = [...availableSlots];
  updated[index].slotTime = newTime;
  updated[index].slotTimeFormatted = newTime; // Cập nhật luôn format nếu dùng
  setAvailableSlots(updated);
};

  const handleAddSlot = () => {
    const newSlot: WorkingTimeSlotResponse = {
      slotId: Date.now(), // hoặc dùng 0 nếu server tạo ID
      slotTime: '',
      slotTimeFormatted: '',
      isAvailable: true,
    };
    setAvailableSlots([...availableSlots, newSlot]);
  };

  const handleRemoveSlot = (index: number) => {
    const updated = [...availableSlots];
    updated.splice(index, 1);
    setAvailableSlots(updated);
  };


  

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-xl overflow-y-auto max-h-[90vh]">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Tạo Lịch Làm Việc</h3>
        <form className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Ngày làm việc</label>
              <input
                type="date"
                value={workDate}
                onChange={e => setWorkDate(e.target.value)}
                className="input text-gray-700"
                required
              />
            </div>
          </div>

         <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Khung giờ trống từ hệ thống
            </label>

            {availableSlots.length === 0 && (
              <p className="text-gray-500 italic text-sm">Không có khung giờ trống.</p>
            )}

            {availableSlots.map((slot, index) => (
              <div key={slot.slotId} className="flex items-center gap-3 mb-2">
                <input
                  type="time"
                  value={slot.slotTime}
                  onChange={e => handleSlotTimeChange(index, e.target.value)}
                  className="border border-gray-300 rounded px-3 py-1 text-gray-700"
                  required
                />
                <button
                  type="button"
                  onClick={() => handleRemoveSlot(index)}
                  className="text-red-600 hover:underline text-sm"
                >
                  Xóa
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={handleAddSlot}
              className="mt-2 text-blue-600 hover:underline text-sm"
            >
              + Thêm khung giờ
            </button>
          </div>



         
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Hủy
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              Lưu
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
